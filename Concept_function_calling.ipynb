from openai import AzureOpenAI
client = AzureOpenAI()

response = client.chat.completions.create(model='PrateekLLM',
                                          messages=[{"role":"user","content":"hi"}])
print(response.choices[0].to_json(indent=2))

import os
import json
import requests

def get_current_weather(city:str)->dict:
    """ this function can be used to fetch weather information for a given city"""
    api_key=os.environ['OPENWEATHERMAP_API_KEY']

    url = f"https://api.openweathermap.org/data/2.5/weather?q={city}&appid={api_key}"
    response = requests.get(url)
    response = response.content.decode() # bytes to string
    response = json.loads(response) # string to dict
    output = {"city":city,
              "weather":response['weather'][0]['description'],
              "temperature":response['main']['temp'],
              "unit":"kelvin"}
    return output


get_current_weather("mumbai")

tools = [{"type":"function",
          "function":{
              "name":"get_current_weather",
              "description":"this function can be used to current weather information for a given city.",
              "parameters":{
                  "type":"object",
                  "properties":{"city":{"type":"string","description":"name of city/location. e.g. mumbai, new york"},
                                },
                                "required":['city'],
              }
          }}]

tool_names = {"get_current_weather":get_current_weather} # name of function in metadata : name of object

def generate_response(prompt):
    messages = [{"role":"system","content":"you are a helpful assistant, who answers questions in concise way"},
                {"role":"user","content":prompt},]
    
    first_response = client.chat.completions.create(model="PrateekLLM",messages=messages,
                                                    temperature=0.1,tools=tools,
                                                    tool_choice='auto')
    
    first_response = first_response.choices[0].message
    if first_response.tool_calls:
        # do something
        print(first_response)
        messages.append(first_response)

        # iterate through all the functions, execute and log responses in messages
        for tool in first_response.tool_calls:
            tool_name = tool.function.name
            tool_args = json.loads(tool.function.arguments) # json.loads converts str to dict
            fun_toexecute = tool_names[tool_name]
            tool_output = fun_toexecute(**tool_args)
            messages.append({"tool_call_id":tool.id,
                             "role":"tool","name":tool_name,"content":json.dumps(tool_output)})
        second_response = client.chat.completions.create(model="PrateekLLM",messages=messages,temperature=0.1)
        return second_response.choices[0].message.content
        
    else:
        return first_response.content

op = generate_response("what is quantum computing")
print(op)

op = generate_response("what is the current weather in new york today.")
print(op)

op = generate_response("what is current time and weather in bengaluru and 591304)")
print(op)



































